<template>
  <a-modal
    :open="visible"
    title="选择创建模式"
    :footer="null"
    :width="600"
    centered
    @cancel="handleCancel"
  >
    <div class="mode-select-modal">
      <div class="mode-option" @click="selectMode('video')">
        <div class="icon video"><i class="iconfont icon-video"></i></div>
        <div class="info">
          <div class="title">视频生成数字人 <span class="tag">推荐高精度</span></div>
          <div class="desc">上传一段视频，精准训练复刻数字人，效果较佳，训练时间约5小时</div>
        </div>
      </div>
      <div class="mode-option" @click="selectMode('image')">
        <div class="icon image"><i class="iconfont icon-star"></i></div>
        <div class="info">
          <div class="title">变装数字人</div>
          <div class="desc">上传人物照片，完善人物信息，生成变装数字人</div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
const props = defineProps<{ visible: boolean }>();
const emit = defineEmits(['close', 'select']);

function handleCancel() {
  emit('close');
}
function selectMode(mode: 'video' | 'image') {
  emit('select', mode);
}
</script>

<style scoped lang="less">
.mode-select-modal {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 16px 0;
  .mode-option {
    display: flex;
    align-items: center;
    background: #f7f8fa;
    border-radius: 12px;
    padding: 18px 24px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: border 0.2s;
    &:hover {
      border: 2px solid #1777ff;
      background: #fff;
    }
    .icon {
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      background: #e6f0ff;
      border-radius: 50%;
      margin-right: 20px;
      &.video {
        background: #e6f0ff;
        color: #1777ff;
      }
      &.image {
        background: #f5f7fa;
        color: #333;
      }
    }
    .info {
      .title {
        font-size: 18px;
        font-weight: 600;
        color: #222;
        .tag {
          font-size: 12px;
          color: #fff;
          background: #ff9800;
          border-radius: 8px;
          padding: 2px 8px;
          margin-left: 8px;
        }
      }
      .desc {
        font-size: 14px;
        color: #888;
        margin-top: 4px;
      }
    }
  }
}
</style>
