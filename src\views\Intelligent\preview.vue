
<script setup lang="ts">
  import {
    agentVersionDetail,
    getAgentDetail,
    getDebuggerFollowupQuestion,
    getFollowupQuestion,
    getLike,
    getDisLike,
    getChatHistoryList,
    delChatData,
  } from '@/api/agent';
  import type { IAgentItem, IMessage } from '@/interface/agent';
  import { ref, onMounted, reactive, onUnmounted, computed, nextTick, createVNode } from 'vue';
  import { useRoute } from 'vue-router';
  import {
    RightCircleOutlined,
    CloseOutlined,
    HistoryOutlined,
    DeleteOutlined,
    ExclamationCircleOutlined,
  } from '@ant-design/icons-vue';
  import { fetchEventSource } from '@microsoft/fetch-event-source';
  import sendBtn from '@/assets/image/base/pictures/sendbtn.png';
  import sendDisableBtn from '@/assets/image/base/pictures/sendDisable.png';
  import Recording from '@/assets/image/base/pictures/voice.gif';
  import { getLocalItem, setLocalItem } from '@/utils/common';
  import { WebSocketClient as webSocketClass } from '@/utils/ws';
  import { recOpen, recStart, recStop } from '@/utils/recorder';
  import config from '@/config';
  //@ts-expect-error
  import MarkdownIt from 'markdown-it';
  import type { ColumnType } from 'ant-design-vue/es/table';
  import { message, Modal } from 'ant-design-vue';
  import { uploadImages } from '@/api/exploration';
  import Icon from '@/components/Icon/index.vue';
  import dayjs from 'dayjs';
  const md = new MarkdownIt({
    html: true, // 允许HTML标签
    linkify: true, // 自动转换URL为链接
    typographer: true, // 美化排版
  });

  interface asrWsProps {
    answer: string;
    isEnd?: boolean;
    isLast?: boolean;
    isFinal?: boolean;
  }

  // const md = new MarkdownIt();
  const route = useRoute();
  const ck = route.query.ck || null;
  const historyList = ref([]);
  const nowChatId = ref('');
  const currentSelectedId = ref(''); // 记录当前选中的会话ID
  interface IBasicState {
    name: string;
    logo: string;
    suggestedQuestions: string[];
    prologue: string;
    background: string;
    deploy_id: string;
    dialogue_rounds: number;
  }
  const image = ref('');
  const basicState = reactive<IBasicState>({
    name: '',
    logo: '',
    suggestedQuestions: [],
    prologue: '',
    background: '',
    deploy_id: '',
    dialogue_rounds: 0,
  });
  const historyShow = ref(false);
  const isMultimodal = ref(false);
  const inputMessage = ref('');
  interface ICitationState {
    type: string;
    visible: boolean;
    content: string | [];
  }
  const citationsState = reactive<ICitationState>({
    visible: false,
    content: [],
    type: 'string',
  });

  // 用于控制流式请求
  const abortController = ref<AbortController | null>(null);
  const spinning = ref(false);
  const isLoading = ref(false);
  const open = ref(false);
  const deployOpen = ref(false);
  const loadingOpen = ref(false);
  const isRecording = ref(false);
  const isImageing = ref(false);
  const messageRef = ref();
  const containerRef = ref();
  const typingInterval = ref();
  const fileInputRef = ref();
  const columns = ref<ColumnType[]>([]);
  // const citations = ref([]);
  const isDebug = computed(() => route.name === 'intelligent-detail');
  const sendParams = reactive({
    instruction: '',
    knowledge_db_ids: [],
    memory_vars: [],
  });
  // 聊天消息数据
  const messages = reactive<IMessage[]>([]);

  // 点赞与点踩状态 - 使用对象存储每个消息的状态
  const feedbackState = reactive<
    Record<
      number,
      {
        isLike: boolean;
        isDislike: boolean;
        likePopoverVisible: boolean;
        dislikePopoverVisible: boolean;
        likeSelectedTags: string[];
        dislikeSelectedTags: string[];
        likeFeedbackText: string;
        dislikeFeedbackText: string;
      }
    >
  >({});

  // 点赞选项
  const likeOptions = ['准确有效', '回答全面', '立场正确'];

  // 点踩选项
  const dislikeOptions = ['虚假信息', '内容缺失', '没有帮助'];

  const handleChange = () => {
    if (inputMessage.value.trim()) {
      open.value = false;
      deployOpen.value = false;
      loadingOpen.value = false;
    }
  };
  // 发送消息
  const handleSend = async (value: string) => {
    open.value = false;
    deployOpen.value = false;
    loadingOpen.value = false;
    if (!value || !value.trim()) {
      open.value = true;
      return;
    }
    if (!basicState.deploy_id) {
      deployOpen.value = true;
      return;
    }
    const lastAssistantMessage = messages.filter((item) => item.role === 'assistant');
    const assistantMessageLength = lastAssistantMessage.length;
    if (isLoading.value || (assistantMessageLength > 0 && !lastAssistantMessage[assistantMessageLength - 1].content)) {
      loadingOpen.value = true;
      return;
    }
    const src = image.value ? JSON.parse(JSON.stringify(image.value)) : '';
    const userMessage = src ? { role: 'user', content: value, image: src } : { role: 'user', content: value };
    messages.push(userMessage);
    inputMessage.value = '';
    image.value = '';
    isImageing.value = false;
    await fetchAIResponse(value);
  };

  // 获取AI流式响应
  const fetchAIResponse = async (value: string) => {
    isLoading.value = true;
    abortController.value = new AbortController();
    const assistantMessage = { role: 'assistant', content: '', citations: [] };
    messages.push(assistantMessage);
    await nextTick(() => {
      if (messageRef.value) {
        messageRef.value[messageRef.value.length - 1].scrollIntoView({ behavior: 'auto', block: 'end' });
      }
    });
    let displayedText = '';
    try {
      const userMessage = messages.filter((item) => item.role === 'user');
      const lastUserMessage = userMessage[userMessage.length - 1];
      const src = lastUserMessage.image || '';
      await fetchEventSource(
        isDebug.value
          ? `/data/ai-platform-backend/api/v1/agent/debug/chat/${route.params.id}`
          : `/data/ai-platform-backend/api/v1/agent/chat/${route.params.id}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: getLocalItem('HQSK_AI_PLATFORM_FRONTEND_TOKEN'),
          },
          body: isDebug.value
            ? JSON.stringify({
                ...sendParams,
                history: messages.slice(0, -2).map((item) => ({ role: item.role, content: item.content })),
                content: src
                  ? [
                      { type: 'image_url', image_url: { url: src } },
                      { type: 'text', text: value },
                    ]
                  : [{ type: 'text', text: value }],
              })
            : JSON.stringify({
                chat_context_id: nowChatId.value ? nowChatId.value : null,
                history: messages.slice(0, -2).map((item) => ({ role: item.role, content: item.content })),
                content: src
                  ? [
                      { type: 'image_url', image_url: { url: src } },
                      { type: 'text', text: value },
                    ]
                  : [{ type: 'text', text: value }],
              }),
          signal: abortController.value.signal,
          // @ts-expect-error
          onopen(response: Response): Promise<void> | void {
            if (!response.ok) {
              throw new Error(`Server returned ${response.status}`);
            }
          },
          onmessage(event: { data: string }): void {
            if (event.data === '[DONE]') return;
            try {
              const content: string = event.data;
              displayedText += content;
              getHistory()
            } catch (err) {
              console.error('Error parsing message:', err);
              throw err;
            }
          },
          onclose(): void {
            // isLoading.value = false;
            abortController.value = null;
          },
          onerror(err: Error): void {
            if (err.name === 'AbortError') {
              console.error('Stream error:', err);
              messages.push({
                role: 'assistant',
                content: '发生错误: ' + err.message,
              });
            }
            isLoading.value = false;
            abortController.value = null;
          },
        },
      );
    } catch (err: unknown) {
      if (err instanceof Error && err.name !== 'AbortError') {
        console.error('Request error:', err);
        messages.push({
          role: 'assistant',
          content: '请求错误: ' + err.message,
        });
      }
      isLoading.value = false;
      abortController.value = null;
    } finally {
      if (!displayedText) {
        messages[messages.length - 1].content = messages[messages.length - 1].content || md.render('服务异常，请稍后重试') ;
        isLoading.value = false;
        return;
      }
      const regex = /<citation>(.*?)<\/citation>/g;
      const match = regex.exec(displayedText);
      if (match !== null && match[1]) {
        messages[messages.length - 1].citations = JSON.parse(match[1]);
      }
      const answer = md.render(displayedText.replace(/<citation>.*?<\/citation>/g, ''));
      const history = JSON.parse(JSON.stringify(messages));
      history[history.length - 1].content = answer;
      // await fetchFollowResponse(history);
      let index = 0;
      async function typeEffect() {
        if (index < answer.length) {
          messages[messages.length - 1].content += answer[index];
          // 将当前字符添加到元素中
          index++;
          // 设置下一次调用的时间间隔
          typingInterval.value = setTimeout(typeEffect, 50); // 每50毫秒打一个字
          await nextTick(() => {
            if (containerRef.value) {
              containerRef.value.scrollIntoView({ behavior: 'auto', block: 'end' });
            }
          });
        } else {
          isLoading.value = false;
          loadingOpen.value = false;
        }
      }
      typeEffect();
      await fetchFollowResponse(history);
    }
  };

  // 获取追问数据
  const fetchFollowResponse = async (history: { role: string; content: string }[]) => {
    if (basicState.dialogue_rounds) {
      const data = isDebug.value
        ? await getDebuggerFollowupQuestion(String(route.params.id), {
            // basicState.dialogue_rounds是轮数，一问一答为一轮 因此乘2
            history: history
              .map((item) => ({ role: item.role, content: item.content }))
              .slice(-(basicState.dialogue_rounds * 2)),
          })
        : await getFollowupQuestion(String(route.params.id), {
            // basicState.dialogue_rounds是轮数，一问一答为一轮 因此乘2
            history: history
              .map((item) => ({ role: item.role, content: item.content }))
              .slice(-(basicState.dialogue_rounds * 2)),
          });
      messages[history.length - 1].followup = data;
      await nextTick(() => {
        if (containerRef.value) {
          containerRef.value.scrollIntoView({ behavior: 'auto', block: 'end' });
        }
      });
    }
  };

  const handleClickCitation = (content: string | Record<string, string>) => {
    Object.assign(citationsState, { visible: true, content, type: typeof content });
    columns.value = getTableColumns();
  };

  const getTableColumns = () => {
    if (!citationsState.content) return [];
    const keys = Object.keys(citationsState.content);
    const columns: ColumnType[] = keys.map((key: string) => {
      return {
        title: key,
        dataIndex: key,
      };
    });
    return columns;
  };
  // 停止生成
  const stopGeneration = () => {
    if (abortController.value) {
      abortController.value.abort();
      abortController.value = null;
    }
    isLoading.value = false;
    if (messages.length && !messages[messages.length - 1].content) {
      messages[messages.length - 1].content = md.render('已停止');
    }
    clearTimeout(typingInterval.value);
    typingInterval.value = null;
    open.value = false;
    deployOpen.value = false;
    loadingOpen.value = false;
  };
  const getAudioToTxt = (data: asrWsProps) => {
    let tempText = data?.answer?.replace(/no/, '');
    inputMessage.value = tempText;
  };
  // 语音识别 WS
  const asrWs = new webSocketClass(config.asr, (data: asrWsProps) => {
    if (data?.answer) {
      getAudioToTxt(data);
    }
  });
  const handleRecorder = () => {
    asrWs.connect();

    recOpen(
      () => {
        recStart();
        isRecording.value = true;
      },
      (res: string) => {
        console.error(res || '获取录音权限失败！');
        message.error('获取录音权限失败！');
      },

      (res) => {
        asrWs?.send(res);
      },
    );
  };

  const stopRecording = () => {
    recStop((res) => {
      asrWs?.send(res);
    });
    isRecording.value = false;
  };

  const cancelRecording = () => {
    recStop(() => {
      inputMessage.value = '';
      asrWs?.close();
    });
    isRecording.value = false;
  };

  const uploadProps = {
    // @ts-expect-error
    beforeUpload: (file: UploadProps['fileList'][number]) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
      if (!isJpgOrPng) {
        message.warn('上传的图片格式不支持！');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('上传的图片太大！');
        return false;
      }
      return isJpgOrPng && isLt10M;
    },
    customRequest: async (detail: { file: File }) => {
      const file = detail.file;
      const formData = new FormData();
      formData.append('files', file);
      const res: { url: string }[] = await uploadImages(formData);
      isImageing.value = true;
      image.value = res[0].url;
    },
    multiple: false,
    fileList: [],
    accept: 'image/png,image/jpg,image/jpeg',
    showUploadList: false,
  };

  // @ts-expect-error
  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      // 这里可以添加文件验证逻辑
      if (file.size > 10 * 1024 * 1024) {
        message.error('文件大小不能超过10MB');
        return;
      }
      const formData = new FormData();
      formData.append('files', file);
      const res: { url: string }[] = await uploadImages(formData);
      image.value = res[0].url;
    }

    // 重置input，以便可以选择同一个文件多次
    e.target.value = null;
  };
  const handleDeleteImage = () => {
    image.value = '';
    isImageing.value = false;
  };
  const handleConfirmUpload = () => {
    console.log(fileInputRef.value);
    fileInputRef.value.click();
  };

  // 处理点赞
  const handleLike = async (message_index: number) => {
    // 初始化该消息的反馈状态
    if (!feedbackState[message_index]) {
      feedbackState[message_index] = {
        isLike: false,
        isDislike: false,
        likePopoverVisible: false,
        dislikePopoverVisible: false,
        likeSelectedTags: [],
        dislikeSelectedTags: [],
        likeFeedbackText: '',
        dislikeFeedbackText: '',
      };
    }

    const currentState = feedbackState[message_index];

    // 如果当前是点赞状态，则取消点赞
    if (currentState.isLike) {
      currentState.isLike = false;
      currentState.likePopoverVisible = false;
      currentState.likeSelectedTags = [];
      currentState.likeFeedbackText = '';
    } else {
      // 如果当前是点踩状态，先取消点踩
      if (currentState.isDislike) {
        currentState.isDislike = false;
        currentState.dislikePopoverVisible = false;
        currentState.dislikeSelectedTags = [];
        currentState.dislikeFeedbackText = '';
      }
      // 设置为点赞状态并显示弹框
      currentState.isLike = true;
      currentState.likePopoverVisible = true;

      // 调用点赞API，即使反馈信息为空
      try {
        await getLike(String(route.params.id), {
          feedback_type: 'good',
          message_index: message_index,
          tags: [],
          detailed_content: '',
        });
      } catch (error) {
        console.error('点赞接口调用失败:', error);
      }
    }
  };

  // 处理点踩
  const handleDislike = async (message_index: number) => {
    // 初始化该消息的反馈状态
    if (!feedbackState[message_index]) {
      feedbackState[message_index] = {
        isLike: false,
        isDislike: false,
        likePopoverVisible: false,
        dislikePopoverVisible: false,
        likeSelectedTags: [],
        dislikeSelectedTags: [],
        likeFeedbackText: '',
        dislikeFeedbackText: '',
      };
    }

    const currentState = feedbackState[message_index];

    // 如果当前是点踩状态，则取消点踩
    if (currentState.isDislike) {
      currentState.isDislike = false;
      currentState.dislikePopoverVisible = false;
      currentState.dislikeSelectedTags = [];
      currentState.dislikeFeedbackText = '';
    } else {
      // 如果当前是点赞状态，先取消点赞
      if (currentState.isLike) {
        currentState.isLike = false;
        currentState.likePopoverVisible = false;
        currentState.likeSelectedTags = [];
        currentState.likeFeedbackText = '';
      }
      // 设置为点踩状态并显示弹框
      currentState.isDislike = true;
      currentState.dislikePopoverVisible = true;

      // 调用点赞API，即使反馈信息为空
      try {
        await getDisLike(String(route.params.id), {
          feedback_type: 'bad',
          message_index: message_index,
          tags: [],
          detailed_content: '',
        });
      } catch (error) {
        console.error('点赞接口调用失败:', error);
      }
    }
  };

  // 切换点赞标签
  const toggleLikeTag = (message_index: number, tagValue: string) => {
    if (!feedbackState[message_index]) return;
    const currentTags = feedbackState[message_index].likeSelectedTags;
    const index = currentTags.indexOf(tagValue);
    if (index > -1) {
      currentTags.splice(index, 1);
    } else {
      currentTags.push(tagValue);
    }
  };

  // 切换点踩标签
  const toggleDislikeTag = (message_index: number, tagValue: string) => {
    if (!feedbackState[message_index]) return;
    const currentTags = feedbackState[message_index].dislikeSelectedTags;
    const index = currentTags.indexOf(tagValue);
    if (index > -1) {
      currentTags.splice(index, 1);
    } else {
      currentTags.push(tagValue);
    }
  };

  // 提交点赞反馈
  const submitLikeFeedback = async (message_index: number) => {
    const currentState = feedbackState[message_index];
    if (!currentState) return;

    const hasContent = currentState.likeSelectedTags.length > 0 || currentState.likeFeedbackText.trim();

    if (!hasContent) {
      message.warning('请选择选项或输入反馈内容');
      return;
    }

    try {
      await getLike(String(route.params.id), {
        feedback_type: 'good',
        message_index: message_index,
        tags: currentState.likeSelectedTags,
        detailed_content: currentState.likeFeedbackText,
      });

      message.success('感谢你的反馈');
      currentState.likePopoverVisible = false;
    } catch (error) {
      message.error('提交失败，请重试');
    }
  };

  // 提交点踩反馈
  const submitDislikeFeedback = async (message_index: number) => {
    const currentState = feedbackState[message_index];
    if (!currentState) return;

    const hasContent = currentState.dislikeSelectedTags.length > 0 || currentState.dislikeFeedbackText.trim();

    if (!hasContent) {
      message.warning('请选择选项或输入反馈内容');
      return;
    }

    try {
      await getDisLike(String(route.params.id), {
        feedback_type: 'bad',
        message_index: message_index,
        tags: currentState.dislikeSelectedTags,
        detailed_content: currentState.dislikeFeedbackText,
      });

      message.success('感谢你的反馈');
      currentState.dislikePopoverVisible = false;
    } catch (error) {
      message.error('提交失败，请重试');
    }
  };

  // 关闭点赞弹框
  const closeLikePopover = (message_index: number) => {
    if (!feedbackState[message_index]) return;
    feedbackState[message_index].likePopoverVisible = false;
  };

  // 关闭点踩弹框
  const closeDislikePopover = (message_index: number) => {
    if (!feedbackState[message_index]) return;
    feedbackState[message_index].dislikePopoverVisible = false;
  };

  // 检查点赞提交按钮是否可点击
  const isLikeSubmitEnabled = (message_index: number) => {
    const currentState = feedbackState[message_index];
    if (!currentState) return false;
    return currentState.likeSelectedTags.length > 0 || currentState.likeFeedbackText.trim();
  };

  // 检查点踩提交按钮是否可点击
  const isDislikeSubmitEnabled = (message_index: number) => {
    const currentState = feedbackState[message_index];
    if (!currentState) return false;
    return currentState.dislikeSelectedTags.length > 0 || currentState.dislikeFeedbackText.trim();
  };

  // 获取点赞反馈文本
  const getLikeFeedbackText = (index: number) => {
    return feedbackState[index]?.likeFeedbackText || '';
  };

  // 获取点踩反馈文本
  const getDislikeFeedbackText = (index: number) => {
    return feedbackState[index]?.dislikeFeedbackText || '';
  };

  // 设置点赞反馈文本
  const setLikeFeedbackText = (index: number, value: string) => {
    if (!feedbackState[index]) {
      feedbackState[index] = {
        isLike: false,
        isDislike: false,
        likePopoverVisible: false,
        dislikePopoverVisible: false,
        likeSelectedTags: [],
        dislikeSelectedTags: [],
        likeFeedbackText: '',
        dislikeFeedbackText: '',
      };
    }
    feedbackState[index].likeFeedbackText = value;
  };

  // 设置点踩反馈文本
  const setDislikeFeedbackText = (index: number, value: string) => {
    if (!feedbackState[index]) {
      feedbackState[index] = {
        isLike: false,
        isDislike: false,
        likePopoverVisible: false,
        dislikePopoverVisible: false,
        likeSelectedTags: [],
        dislikeSelectedTags: [],
        likeFeedbackText: '',
        dislikeFeedbackText: '',
      };
    }
    feedbackState[index].dislikeFeedbackText = value;
  };

  // 组件卸载时中止请求
  onUnmounted(() => {
    stopGeneration();
    clearTimeout(typingInterval.value);
    typingInterval.value = null;
  });

  onMounted(async () => {
    if (ck) {
      setLocalItem('HQSK_AI_PLATFORM_FRONTEND_TOKEN', ck as string);
    }
    spinning.value = true;
    try {
      const data: IAgentItem =
        route.name === 'intelligent-detail'
          ? await agentVersionDetail(String(route.params.id))
          : await getAgentDetail(String(route.params.id), true);
      const { icon_url, name, deploy_id, config, model_category } = data;
      isMultimodal.value = model_category === 'multimodal';
      const {
        instruction,
        memory_config,
        knowledge_db,
        opener_prompt,
        suggested_questions,
        background_image,
        followup_config,
      } = config;
      Object.assign(basicState, { logo: icon_url, name, deploy_id });
      if (isDebug.value) {
        Object.assign(sendParams, {
          instruction: instruction.value || '',
          // 在调试中，知识库开关开了才传知识库id
          knowledge_db_ids: knowledge_db.is_enable
            ? knowledge_db.value
              ? knowledge_db.value.map((item) => item.id)
              : []
            : [],
          memory_vars: memory_config.value
            ? memory_config.value.map((item) => {
                return {
                  ...item,
                  current_value: '',
                };
              })
            : [],
        });
      }
      if (opener_prompt.is_enable) {
        basicState.prologue = opener_prompt.value;
      }
      if (background_image.is_enable) {
        basicState.background = background_image.value;
      }
      if (suggested_questions.is_enable) {
        basicState.suggestedQuestions = suggested_questions.value;
      }
      if (followup_config && followup_config.is_enable) {
        basicState.dialogue_rounds = followup_config.value.dialogue_rounds;
      }
    } catch {
      message.warn('应用异常，请联系管理员');
    }

    spinning.value = false;
    getHistory();
  });
  // 删除历史会话
  const showDeleteConfirm = (id, index, text) => {
    Modal.confirm({
      title: `确定删除会话 "${text}"？`,
      icon: createVNode(ExclamationCircleOutlined),
      content: '删除后不可恢复',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        delChatData(id).then((res) => {
          if (currentSelectedId.value === id) {
            currentSelectedId.value = ''; // 清除选中状态
          }
          message.success('删除成功');
          getHistory();
          if (messages.length > 0 && nowChatId.value && index + 1 < historyList.value.length) {
            currentSelectedId.value = historyList.value[index + 1]?.id;
            resetNowchat(historyList.value[index + 1]?.history, historyList.value[index + 1]?.id);
          } else if (messages.length > 0 && nowChatId.value && index + 1 === historyList.value.length) {
            currentSelectedId.value = historyList.value[index - 1]?.id;
            resetNowchat(historyList.value[index - 1]?.history, historyList.value[index - 1]?.id);
          } else if (messages.length > 0 && nowChatId.value) {
            messages.splice(0, messages.length);
          }
        });
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };
  // 获取历史会话
  const getHistory = async () => {
    const chatId = String(route.params.id);
    const res = await getChatHistoryList(chatId, true);
    historyList.value = res;
  };

  // 处理时间的函数
  const formatTime = (updatedAt) => {
    // 解析目标时间（自动处理时区）
    const targetTime = dayjs(updatedAt);
    // 获取当前时间
    const currentTime = dayjs();

    // 判断是否是今天（年月日相同）
    if (targetTime.isSame(currentTime, 'day')) {
      return '今天';
    }
    // 判断是否是昨天（目标时间 = 当前时间 - 1天，且年月日相同）
    else if (targetTime.isSame(currentTime.subtract(1, 'day'), 'day')) {
      return '昨天';
    }
    // 超过昨天则显示具体日期（格式：YYYY-MM-DD）
    else {
      return targetTime.format('YYYY-MM-DD');
    }
  };
  // 处理接口返回的历史会话数据
  function getFirstText(data) {
    try {
      // 1. 替换单引号为双引号（JSON要求双引号）
      let jsonStr = data.replace(/'/g, '"');
      // 2. 处理转义字符（避免解析错误）
      jsonStr = jsonStr.replace(/\\\\n/g, '\\n').replace(/\\\\r/g, '\\r');

      // 3. 解析为JavaScript数组
      const arr = JSON.parse(jsonStr);
      // 4. 取数组第一条元素
      const firstItem = arr[0];
      if (!firstItem) return '';

      // 5. 提取text（根据结构：第一条的content是字符串，直接作为text）
      // 若content是数组（如后续元素），则取content[0].text
      let text = '';
      if (Array.isArray(firstItem.content)) {
        // 处理content为数组的情况（如[{type: 'text', text: ...}]）
        text = firstItem.content[0]?.text || '';
      } else {
        // 处理content为字符串的情况（第一条属于这种）
        text = firstItem.content;
      }
      return text;
    } catch (err) {
      console.error('解析失败：', err);
      return '解析错误';
    }
  }

  const filterContent = (item) => {
    if (typeof item.history !== 'string') {
      return item.history[0]?.content[0]?.text || item.history[0]?.content;
    } else {
      return getFirstText(item.history);
    }
  };
  // 点击历史会话可重新对话
  const resetNowchat = (historyData, id) => {
    nowChatId.value = id;
    currentSelectedId.value = id;

    // 1. 先清空原数组
    messages.splice(0, messages.length);

    // 2. 处理历史数据（确保结构正确）
    let newMessages: IMessage[];
    if (typeof historyData !== 'string') {
      newMessages = historyData as IMessage[]; // 假设 historyData 是 IMessage[] 类型
    } else {
      const jsonStr = historyData.replace(/'/g, '"');
      newMessages = JSON.parse(jsonStr) as IMessage[];
    }
    const voilMessage = newMessages.map((item) => {
      let content = '';
      if (Array.isArray(item.content)) {
        content = item.content[0].text;
      } else {
        content = item.content;
      }
      return {
        content,
        role: item.role,
        citations: item?.citations,
      };
    });
    messages.push(...voilMessage);
  };
</script>

<template>
  <div v-if="spinning" class="w-100% h-100% flex items-center justify-center">
    <a-spin />
  </div>
  <div
    v-else
    class="preview-container"
    :style="{
      backgroundImage: !isDebug && basicState.background ? `url(${basicState.background})` : '',
      backgroundSize: 'cover',
      backgroundRepeat: 'no-repeat',
      backgroundPosition: 'center',
    }"
  >
    <div
      class="preview-content"
      :style="{
        width: isDebug ? '100%' : '60vw',
        backgroundImage: isDebug && basicState.background ? `url(${basicState.background})` : '',
        backgroundSize: isDebug ? 'cover' : '',
        backgroundRepeat: isDebug ? 'no-repeat' : '',
        backgroundPosition: isDebug ? 'center' : '',
      }"
    >
      <div class="listory">
        <a-tooltip
          :title="!getLocalItem('HQSK_AI_PLATFORM_FRONTEND_TOKEN') ? '请登录后使用' : '历史会话'"
          :disabled="getLocalItem('HQSK_AI_PLATFORM_FRONTEND_TOKEN')"
          placement="bottom"
        >
          <HistoryOutlined
            :style="{
              fontSize: '24px',
              color: getLocalItem('HQSK_AI_PLATFORM_FRONTEND_TOKEN') ? '#797979' : '#ccc',
              cursor: getLocalItem('HQSK_AI_PLATFORM_FRONTEND_TOKEN') ? 'pointer' : 'not-allowed',
            }"
            @click="getLocalItem('HQSK_AI_PLATFORM_FRONTEND_TOKEN') && (historyShow = true)"
          />
        </a-tooltip>
      </div>
      <!-- <div class="content">

      </div> -->
      <div class="chat-container overflow-scroll">
        <div class="header">
          <!-- <img :src="basicState.logo" alt="" /> -->
          <div class="img-content" :style="{ backgroundImage: `url(${basicState.logo})` }" />
          <div class="text-24px mt-10px">{{ basicState.name }}</div>
        </div>
        <div v-if="basicState.prologue || basicState.suggestedQuestions.length" class="prologue">
          <div v-if="basicState.prologue" class="mb-10px leading-24px text-16px text-#17181A">
            {{ basicState.prologue }}
          </div>
          <div v-if="basicState.suggestedQuestions.length" class="flex flex-col items-start">
            <div
              v-for="(item, index) in basicState.suggestedQuestions"
              :key="index"
              class="question-item"
              @click="handleSend(item)"
            >
              <div class="question-item-msg">{{ item }}</div>
              <RightCircleOutlined />
            </div>
          </div>
        </div>
        <div ref="containerRef" class="messages flex flex-col mt-10px">
          <div
            v-for="(message, index) in messages"
            :key="index"
            ref="messageRef"
            class="message"
            :style="{
              justifyContent: message.role === 'user' ? 'flex-end' : 'start',
              flexDirection: message.role === 'user' ? 'row' : 'column',
            }"
          >
            <div v-if="message.content" :class="message.role" class="message-content">
              <div
                v-if="message.role === 'user' && message.image"
                class="img-box"
                :style="{ backgroundImage: `url(${message.image})` }"
              ></div>
              <!-- <div v-html="message.content.replace(/<citation>.*?<\/citation>/g, '')"></div> -->
              <div v-html="message.content.replace(/<citation>[\s\S]*?<\/citation>/g, '')"></div>
              <div v-if="message.role === 'assistant' && message.citations && message.citations.length">
                <div class="ml-10px mb-10px text-#797979">{{ `切片（${message.citations.length}）` }}</div>
                <div class="citation flex">
                  <div
                    v-for="(item, index) in message.citations"
                    :key="item"
                    class="citation-item"
                    @click="handleClickCitation(item)"
                    @cancel="citationsState.visible = false"
                  >
                    {{ typeof item === 'string' ? item : `切片信息${index + 1}` }}
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="message-content"><a-spin /></div>
            <!-- 反馈按钮放在最后，只对助手回答显示 -->
            <div v-if="message.role === 'assistant' && message.content" class="feedback-buttons">
              <!-- 点赞反馈 -->
              <a-popover
                :open="feedbackState[index]?.likePopoverVisible"
                placement="top"
                trigger="click"
                :overlay-style="{ width: '360px' }"
                @open-change="(visible) => (visible ? null : closeLikePopover(index))"
              >
                <template #content>
                  <div class="feedback-popover">
                    <div class="feedback-header">
                      <span class="feedback-title">谢谢你的认可,你的反馈,我们进步的动力</span>
                      <CloseOutlined class="close-icon" @click="closeLikePopover(index)" />
                    </div>
                    <div class="feedback-options">
                      <div class="tag-list">
                        <a-button
                          v-for="option in likeOptions"
                          :key="option"
                          :type="feedbackState[index]?.likeSelectedTags.includes(option) ? 'primary' : 'default'"
                          size="small"
                          class="feedback-tag"
                          @click="toggleLikeTag(index, option)"
                        >
                          {{ option }}
                        </a-button>
                      </div>
                    </div>
                    <div class="feedback-textarea">
                      <a-textarea
                        :value="getLikeFeedbackText(index)"
                        placeholder="请写下更多反馈内容"
                        :auto-size="{ minRows: 3, maxRows: 6 }"
                        @input="(e) => setLikeFeedbackText(index, e.target.value)"
                      />
                    </div>
                    <div class="feedback-submit">
                      <a-button
                        class="submit-btn"
                        type="primary"
                        :disabled="!isLikeSubmitEnabled(index)"
                        @click="submitLikeFeedback(index)"
                      >
                        提交
                      </a-button>
                    </div>
                  </div>
                </template>
                <template #default>
                  <Icon
                    :name="feedbackState[index]?.isLike ? 'xuanzhong-manyi' : 'manyi'"
                    size="24"
                    class="feedback-icon like-icon"
                    style="padding: 4px; cursor: pointer"
                    @click="handleLike(index)"
                  />
                </template>
              </a-popover>

              <!-- 点踩反馈 -->
              <a-popover
                :open="feedbackState[index]?.dislikePopoverVisible"
                placement="top"
                trigger="click"
                :overlay-style="{ width: '360px' }"
                @open-change="(visible) => (visible ? null : closeDislikePopover(index))"
              >
                <template #content>
                  <div class="feedback-popover">
                    <div class="feedback-header">
                      <span class="feedback-title">请反馈你遇到的问题</span>
                      <CloseOutlined class="close-icon" @click="closeDislikePopover(index)" />
                    </div>
                    <div class="feedback-options">
                      <div class="tag-list">
                        <a-button
                          v-for="option in dislikeOptions"
                          :key="option"
                          :type="feedbackState[index]?.dislikeSelectedTags.includes(option) ? 'primary' : 'default'"
                          size="small"
                          class="feedback-tag"
                          @click="toggleDislikeTag(index, option)"
                        >
                          {{ option }}
                        </a-button>
                      </div>
                    </div>
                    <div class="feedback-textarea">
                      <a-textarea
                        :value="getDislikeFeedbackText(index)"
                        placeholder="请写下更多反馈内容"
                        :auto-size="{ minRows: 3, maxRows: 6 }"
                        @input="(e) => setDislikeFeedbackText(index, e.target.value)"
                      />
                    </div>
                    <div class="feedback-submit">
                      <a-button
                        class="submit-btn"
                        type="primary"
                        :disabled="!isDislikeSubmitEnabled(index)"
                        @click="submitDislikeFeedback(index)"
                      >
                        提交
                      </a-button>
                    </div>
                  </div>
                </template>
                <template #default>
                  <Icon
                    :name="feedbackState[index]?.isDislike ? 'xuanzhong-bumanyi' : 'bumanyi'"
                    size="24"
                    class="feedback-icon dislike-icon"
                    @click="handleDislike(index)"
                  />
                </template>
              </a-popover>
            </div>

            <div v-show="message.role === 'assistant' && message.followup && message.followup.length">
              <!-- followup -->
              <div class="followup flex flex-col">
                <div class="ml-10px m-y-10px text-#666666 text-16px font-400">继续问</div>
                <div v-for="item in message.followup" :key="item" class="followup-item" @click="handleSend(item)">
                  {{ item }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="noLogin" v-if="!getLocalItem('HQSK_AI_PLATFORM_FRONTEND_TOKEN')">
        您当前还未登录，登录后开启更多对话
      </div>
      <div
        class="chat-box"
        :style="{
          flexDirection: isImageing ? 'column' : 'row',
          padding: isImageing || isRecording ? '10px' : '0 10px',
        }"
      >
        <div v-if="isImageing">
          <div class="img-box" :style="{ backgroundImage: `url(${image})` }">
            <div class="closeIcon" @click="handleDeleteImage">
              <CloseOutlined />
            </div>
          </div>
        </div>
        <div class="flex items-center w-100%" :style="{ flexDirection: isRecording ? 'column' : 'row' }">
          <a-textarea
            v-model:value="inputMessage"
            class="custom-textarea"
            placeholder="请输入你的问题"
            :auto-size="{ minRows: 1, maxRows: 4 }"
            @change="handleChange"
            @press-enter.prevent="handleSend(inputMessage)"
          />

          <div v-if="isRecording" class="voice-box">
            <div class="custom-textarea">
              <a-button class="exit-recording" @click="cancelRecording">清空</a-button>

              <div class="recording-status">
                <img class="waveform" :src="Recording" alt="录音波形" />
                <span class="recording-text">录音中</span>
              </div>
              <a-button type="primary" class="exit-recording1" @click="stopRecording">确认</a-button>
            </div>
          </div>

          <div v-else class="flex flex-justify-end items-center">
            <template v-if="isMultimodal">
              <!-- 隐藏的文件输入 -->
              <input ref="fileInputRef" type="file" style="display: none" @change="handleFileChange" />
              <a-popconfirm
                v-if="image"
                title="文件覆盖提示?"
                description="新上传的文件会覆盖原有文件，是否继续上传"
                @confirm="handleConfirmUpload"
              >
                <Icon name="shangchuantupian" size="32px" style="color: #797979; cursor: pointer" />
              </a-popconfirm>
              <a-upload v-else class="avatar-uploader" v-bind="uploadProps" list-type="text">
                <Icon name="shangchuantupian" size="32px" style="margin-right: 10px; color: #797979; cursor: pointer" />
              </a-upload>
            </template>
            <Icon name="luyin3" size="32px" style="cursor: pointer" @click="handleRecorder" />
            <div class="divider m-x-10px"></div>
            <template v-if="isLoading || (messages.length && !messages[messages.length - 1].content)">
              <a-tooltip title="停止生成" placement="top">
                <Icon size="32" name="tingzhishengcheng" @click="stopGeneration" />
              </a-tooltip>
            </template>
            <template v-else>
              <a-tooltip :arrow-point-at-center="true" :open="open || deployOpen || loadingOpen">
                <template #title>{{
                  open ? '请输入您的问题' : deployOpen ? '模型未配置，请配置后再输入' : ''
                }}</template>
                <img
                  v-if="!basicState.deploy_id || !inputMessage || !inputMessage.trim()"
                  class="send-disabled-icon"
                  :src="sendDisableBtn"
                  alt=""
                  :preview="false"
                />
                <img
                  v-else
                  class="send-icon"
                  :src="sendBtn"
                  alt=""
                  :preview="false"
                  @click="handleSend(inputMessage)"
                />
              </a-tooltip>
            </template>
          </div>
        </div>
      </div>
      <span class="flex justify-center align-center mb-10px text-#999999 text-12px">以上内容均由AI生成，仅供参考</span>
    </div>
  </div>

  <a-modal v-model:open="citationsState.visible" width="50%" centered title="回答来源" :footer="false">
    <div v-if="citationsState.type === 'string'" class="citation-model overflow-scroll">
      {{ citationsState.content }}
    </div>
    <a-table v-else :data-source="[citationsState.content]" :columns="columns" :scroll="{ x: 'max-content' }">
      <template #bodyCell="{ text }">
        {{ String(text) }}
      </template>
    </a-table>
  </a-modal>

  <a-modal v-model:open="historyShow" title="历史会话" :footer="null">
    <div class="msgcontent">
      <template v-if="historyList.length > 0">
        <div
          class="msgItem"
          v-for="(item, index) in historyList"
          :key="index"
          @click="resetNowchat(item.history, item.id)"
          :class="{ 'msgItem-selected': currentSelectedId === item.id }"
        >
          <div class="showText">
            {{ filterContent(item) }}
          </div>
          <div>
            <span style="margin-right: 10px">{{ formatTime(item.updated_at) }}</span>
            <DeleteOutlined class="del" @click.stop="showDeleteConfirm(item.id, index, filterContent(item))" />
          </div>
        </div>
      </template>
      <a-empty description="暂无历史会话" v-else />
    </div>
  </a-modal>
</template>

<style scoped lang="less">
  .msgcontent {
    padding: 20px 0;
    max-height: 500px;
    overflow-y: auto;
    .msgItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 50px;
      padding: 0 13px;
      border-radius: 4px;
      &.msgItem-selected {
        background: #e6f7ff; // 选中背景色
        border-left: 3px solid #1890ff; // 选中左侧标识
      }

      &:hover:not(.msgItem-selected) {
        background: #bcc1c9;
      }

      &:active:not(.msgItem-selected) {
        background: #6880a1;
      }
      .del {
        cursor: pointer;
        font-size: 14px;
        color: #f5222d; /* 红色更醒目 */
        display: none; /* 默认隐藏 */
        transition: opacity 0.2s; /* 过渡效果更自然 */
      }

      // 鼠标移入时显示删除图标
      &:hover .del {
        display: inline-block;
        opacity: 1;
      }

      // 点击选中状态时也保持显示（配合之前的选中样式）
      &.msgItem-selected .del {
        display: inline-block;
        opacity: 1;
      }
      .showText {
        width: 300px;
        overflow: hidden; /*超出部分隐藏*/
        white-space: nowrap; /*禁止换行*/
        text-overflow: ellipsis; /*省略号*/
      }
      .del {
        cursor: pointer;
        font-size: 14px;
      }
    }
  }
  .preview-container {
    width: 100%;
    height: 100%; /* 高度自适应 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .preview-content {
    height: 100%; /* 高度自适应 */
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    padding: 20px;
    padding-bottom: 0;
    display: flex;
    flex-direction: column;
    position: relative;
    .listory {
      right: 30px;
      top: 20px;
      position: absolute;
    }
  }
  .content {
    padding-bottom: 15px;
  }
  .header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    // img {
    //   width: 96px;
    //   height: 96px;
    // }

    .img-content {
      width: 96px; /* 宽度自适应 */
      height: 96px; /* 高度自适应 */
      background-color: #fff;
      border-radius: 10px;
      // background-image: url('your-image.jpg');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
    }
  }
  .prologue {
    background: #fff;
    padding: 20px 20px 8px 20px;
    border: 1px solid #ccc;
    border-radius: 10px;
    max-height: 200px;
    .question-item {
      display: flex;
      cursor: pointer;
      justify-content: space-between;
      width: auto;
      padding: 10px;
      font-size: 14px;
      line-height: 14px;
      background: #e0edff;
      text-align: left;
      color: #1777ff;
      font-weight: 400;
      border-radius: 16px;
      margin-bottom: 12px;
      margin-left: -8px;

      .question-item-msg {
        margin-right: 10px;
      }
    }
  }

  .chat-container {
    flex: 1;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none;  /* IE/Edge */
    .messages {
      display: flex;
      flex-direction: column;
      overflow-anchor: auto; /* 关键属性，让滚动条自动跟随新内容 */
      .message {
        display: flex;
        margin-bottom: 10px;
        .message-content {
          // padding: 10px;
          max-width: 80%;
          line-height: 20px;
          word-break: break-all;
          // background-color: #fff;
          // border: 1px solid #eee;
          border-radius: 10px;
        }
        .user {
          background-color: #1777ff;
          padding: 20px;
          border-radius: 10px 10px 0px 10px;
          backdrop-filter: blur(4px);
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          line-height: 16px;
          text-align: right;
          font-style: normal;
          // border-bottom-right-radius: 0;
        }
        .assistant {
          display: inline-block;
          width: auto;
          align-self: flex-start;
          background: rgba(255,255,255,0.8);
          padding: 18px 11px 18px 20px;
          border-radius: 10px 10px 10px 0px;
          backdrop-filter: blur(4px);
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 16px;
          color: #17181A;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          // border-bottom-left-radius: 0;

          :deep(p){
            margin: 0;
          }
        }
      }
    }
  }
  .noLogin {
    margin-bottom: 20px;
    color: #999;
  }
  .chat-box {
    position: relative;
    box-sizing: border-box;
    display: flex;
    // align-items: center;
    width: 100%;
    height: auto;
    padding: 0 10px;
    margin-bottom: 6px;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);

    :deep(.custom-textarea) {
      position: relative;
      box-sizing: border-box;
      height: auto;
      padding: 10px;
      font-family: PingFangSC, 'PingFang SC';
      font-size: 16px;
      font-weight: 400;
      background: rgb(255 255 255 / 20%);
      border-radius: 6px;
      overflow: hidden; /* 隐藏滚动条 */
      resize: none; /* 禁止用户调整大小 */

      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        display: none;
      }

      /* 占位符样式 */
      &::placeholder {
        color: #cccccc;
      }
    }
    .divider {
      width: 1px;
      height: 24px;
      background: #969799;
      border-radius: 1px;
    }
    .video-icon,
    .send-icon,
    .send-disabled-icon {
      width: 32px;
      height: 32px;
      cursor: pointer;
    }
    .send-disabled-icon {
      cursor: not-allowed;
    }
    .voice-box {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 48px;
      padding: 0 16px;
      background: rgba(233, 236, 242, 0.7);
      border-radius: 6px;
      backdrop-filter: blur(4px);

      .custom-textarea {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .exit-recording {
          font-size: 14px;
          font-weight: 500;
          color: #666;
          margin-right: 12px;
        }
        .exit-recording1 {
          font-size: 14px;
          font-weight: 500;
          color: #fff;
          margin-right: 12px;
        }

        .recording-status {
          display: flex;
          align-items: center;
          gap: 8px;

          .waveform {
            height: 24px;
            width: auto;
          }

          .recording-text {
            font-size: 14px;
            font-weight: 500;
            color: #1777ff;
          }
        }

        .send-icon {
          width: 32px;
          height: 32px;
          cursor: pointer;
        }
      }
    }
  }

  :deep(.ant-input) {
    border: none;
    &:focus {
      box-shadow: none !important;
    }
  }
  .citation-item,
  .followup-item {
    max-width: calc(50% - 10px);
    white-space: nowrap; /* 禁止换行 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis;
    cursor: pointer;
    background-color: #f2f8ff;
    color: #1777ff;
    border-radius: 20px;
    margin-bottom: 5px;
    padding: 12px;
    margin: 0 5px;
  }

  .followup-item {
    // 让宽度随文字长度自适应
    display: inline-block;
    width: auto;
    align-self: flex-start; // 避免在父容器为 flex-col 时被拉伸为整行
    max-width: none; // 覆盖上方 .citation-item, .followup-item 的 max-width 限制
    white-space: wrap;
    overflow: visible;
    text-overflow: clip;

    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #17181a;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    backdrop-filter: blur(4px);
    margin-bottom: 12px;

    &:hover {
      color: #1777ff;
      background: #e0edff;
    }
  }

  .citation-model {
    max-height: 400px;
    padding: 10px;
  }

  .img-box {
    position: relative;
    width: 100px;
    height: 100px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;

    .closeIcon {
      position: absolute;
      top: 0;
      right: 0;
      cursor: pointer;
    }
  }
  .feedback-buttons {
    display: flex;
    gap: 10px;
    margin-top: 8px;
    margin-left: 10px;

    .feedback-icon {
      cursor: pointer;
      font-size: 16px;
      color: #797979;
      transition: color 0.2s;
      outline: none; // 移除焦点边框
      border: none; // 移除边框
      background: none; // 移除背景

      &:hover {
        background: rgba(255,255,255,0.8);
        border-radius: 6px;
      }

      &:focus {
        outline: none; // 移除焦点边框
        border: none; // 移除边框
        box-shadow: none; // 移除阴影
      }

      &:active {
        outline: none; // 移除激活状态边框
        border: none; // 移除边框
        box-shadow: none; // 移除阴影
      }
    }
  }

  // 反馈弹框样式
  .feedback-popover {
    padding: 0;

    .feedback-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 16px 12px;

      .feedback-title {
        font-size: 14px;
        font-weight: 500;
        color: #17181a;
        line-height: 20px;
      }

      .close-icon {
        cursor: pointer;
        color: #999;
        font-size: 12px;
        padding: 4px;
        border-radius: 2px;
        transition: all 0.2s;
        // border: 1px solid #999;

        &:hover {
          color: #666;
          background-color: #f5f5f5;
        }
      }
    }

    .feedback-options {
      padding: 12px 16px;

      .tag-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .feedback-tag {
          margin: 0;
          border-radius: 10px;
          font-size: 12px;
          height: 28px;
          padding: 0 12px;
          border: 1px solid #d9d9d9;
          background: #f5f5f5;
          color: #666;

          &:hover {
            border-color: #1777ff;
            color: #1777ff;
          }

          &.ant-btn-primary {
            background: #1777ff;
            border-color: #1777ff;
            color: #fff;

            &:hover {
              background: #4096ff;
              border-color: #4096ff;
            }
          }
        }
      }
    }

    .feedback-textarea {
      padding: 0 16px 12px;

      :deep(.ant-input) {
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        font-size: 12px;
        resize: none;

        &:focus {
          border-color: #1777ff;
          box-shadow: 0 0 0 2px rgba(23, 119, 255, 0.2);
        }

        &::placeholder {
          color: #bfbfbf;
        }
      }
    }

    .feedback-submit {
      padding: 0 16px 16px;
      text-align: center;

      .submit-btn {
        height: 32px;
        font-size: 14px;
        font-weight: 500;
        border-radius: 6px;

        &:disabled {
          background-color: #f5f5f5;
          border-color: #d9d9d9;
          color: #bfbfbf;
          cursor: not-allowed;
        }

        &:not(:disabled) {
          background-color: #1777ff;
          border-color: #1777ff;

          &:hover {
            background-color: #4096ff;
            border-color: #4096ff;
          }
        }
      }
    }
  }

  // 自定义Popover样式
  :deep(.ant-popover) {
    .ant-popover-inner {
      width: 420px;
      padding: 0;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .ant-popover-arrow {
      display: none;
    }
  }

  // 移除Icon组件的边框
  :deep(.feedback-icon) {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;

    &:focus {
      outline: none !important;
      border: none !important;
      box-shadow: none !important;
    }

    &:active {
      outline: none !important;
      border: none !important;
      box-shadow: none !important;
    }
  }
</style>


