<template>
  <a-modal :open="visible" title="视频生成数字人" :footer="null" :width="600" centered @cancel="handleCancel">
    <div style="padding: 40px; text-align: center;">
      <h2>视频生成数字人</h2>
      <p>这里是视频生成数字人的内容区域，后续可根据需求完善。</p>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
const props = defineProps<{ visible: boolean }>();
const emit = defineEmits(['close']);
function handleCancel() {
  emit('close');
}
</script>
